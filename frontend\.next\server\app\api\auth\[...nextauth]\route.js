var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/auth/[...nextauth]/route.js")
R.c("server/chunks/94728_next_4d25308c._.js")
R.c("server/chunks/07131_@auth_core_573515e8._.js")
R.c("server/chunks/836f6_jose_dist_webapi_f1488b1b._.js")
R.c("server/chunks/948cc_zod_v4_9fd5a225._.js")
R.c("server/chunks/9e883__pnpm_5c04af0f._.js")
R.c("server/chunks/[root-of-the-server]__a19c7b00._.js")
R.m("[project]/frontend/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/frontend/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/frontend/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
