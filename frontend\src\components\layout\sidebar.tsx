"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import {
	Home,
	Users,
	Settings,
	LogOut,
	Menu,
	X,
	ChevronLeft,
	ChevronRight,
	Calendar,
	Wallet,
	BarChart3,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const navigation = [
	{ name: "Dashboard", href: "/dashboard", icon: Home },
	{
		name: "Membre<PERSON>",
		href: "/dashboard/members",
		icon: Users,
		roles: ["admin", "tresorier"],
	},
	{
		name: "Sessions",
		href: "/dashboard/sessions",
		icon: Calendar,
		roles: ["admin", "tresorier", "membre"],
	},
	{
		name: "<PERSON>ais<PERSON>",
		href: "/dashboard/caisses",
		icon: Wallet,
		roles: ["admin", "tresorier"],
	},
	{
		name: "Rapports",
		href: "/dashboard/reports",
		icon: BarChart3,
		roles: ["admin", "tresorier"],
	},
	{
		name: "<PERSON><PERSON><PERSON><PERSON>",
		href: "/dashboard/settings",
		icon: Settings,
		roles: ["admin"],
	},
];

interface SidebarProps {
	className?: string;
}

export function Sidebar({ className }: SidebarProps) {
	const [collapsed, setCollapsed] = useState(false);
	const pathname = usePathname();
	const { data: session } = useSession();

	const handleSignOut = () => {
		signOut({ callbackUrl: "/auth/signin" });
	};

	return (
		<div
			className={cn(
				"flex flex-col bg-white border-r border-gray-200 transition-all duration-300",
				collapsed ? "w-16" : "w-64",
				className,
			)}
		>
			{/* Header */}
			<div className="flex items-center justify-between p-4 border-b border-gray-200">
				{!collapsed && (
					<h1 className="text-xl font-semibold text-gray-900">Tontine</h1>
				)}
				<Button
					variant="ghost"
					size="icon"
					onClick={() => setCollapsed(!collapsed)}
					className="h-8 w-8"
				>
					{collapsed ? (
						<ChevronRight className="h-4 w-4" />
					) : (
						<ChevronLeft className="h-4 w-4" />
					)}
				</Button>
			</div>

			{/* Navigation */}
			<nav className={cn("flex-1 p-4 space-y-2", collapsed && "p-2")}>
				{navigation
					.filter((item) => {
						// Afficher tous les éléments sans rôles spécifiés
						if (!(item as any).roles) return true;

						// Vérifier si l'utilisateur a le bon rôle
						const userRole = (session?.user as any)?.role;
						return userRole && (item as any).roles.includes(userRole);
					})
					.map((item) => {
						const Icon = item.icon;
						const isActive =
							pathname === item.href || pathname.startsWith(item.href + "/");

						return (
							<Link
								key={item.name}
								href={item.href}
								className={cn(
									"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
									isActive
										? "bg-blue-100 text-blue-700"
										: "text-gray-600 hover:bg-gray-100 hover:text-gray-900",
									collapsed && "justify-center px-1 py-2",
								)}
							>
								<Icon className={cn("h-5 w-5", !collapsed && "mr-3")} />
								{!collapsed && item.name}
							</Link>
						);
					})}
			</nav>

			{/* User section */}
			<div className="p-4 border-t border-gray-200">
				{session?.user && (
					<div className={cn("mb-3", collapsed && "text-center")}>
						{!collapsed && (
							<div>
								<p className="text-sm font-medium text-gray-900">
									{session.user.name}
								</p>
								<p className="text-xs text-gray-500">{session.user.email}</p>
							</div>
						)}
					</div>
				)}
				<Button
					variant="ghost"
					onClick={handleSignOut}
					className={cn(
						"w-full justify-start text-gray-600 hover:text-gray-900",
						collapsed && "justify-center px-0",
					)}
				>
					<LogOut className={cn("h-4 w-4", !collapsed && "mr-2")} />
					{!collapsed && "Sign out"}
				</Button>
			</div>
		</div>
	);
}
